// 工具函数：等待元素出现
function waitForElement(selector, timeout = 10000) {
    return new Promise((resolve, reject) => {
        const element = document.querySelector(selector);
        if (element) {
            resolve(element);
            return;
        }
        
        const observer = new MutationObserver((mutations, obs) => {
            const element = document.querySelector(selector);
            if (element) {
                obs.disconnect();
                resolve(element);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        setTimeout(() => {
            observer.disconnect();
            reject(new Error(`Element ${selector} not found within ${timeout}ms`));
        }, timeout);
    });
}

// 工具函数：等待页面加载完成
function waitForPageLoad(timeout = 15000) {
    return new Promise((resolve) => {
        if (document.readyState === 'complete') {
            setTimeout(resolve, 1000); // 额外等待1秒确保页面完全加载
            return;
        }
        
        const checkLoad = () => {
            if (document.readyState === 'complete') {
                setTimeout(resolve, 1000);
            } else {
                setTimeout(checkLoad, 100);
            }
        };
        
        checkLoad();
        
        // 超时保护
        setTimeout(resolve, timeout);
    });
}

// 工具函数：点击元素
function clickElement(element) {
    return new Promise((resolve) => {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        setTimeout(() => {
            element.click();
            resolve();
        }, 500);
    });
}

// 显示完成提示
function showCompletionMessage() {
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 10000;
        display: flex;
        justify-content: center;
        align-items: center;
    `;
    
    const message = document.createElement('div');
    message.style.cssText = `
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        text-align: center;
        font-family: "Microsoft YaHei", sans-serif;
        font-size: 18px;
        color: #4CAF50;
        font-weight: bold;
    `;
    message.textContent = '✅ 操作完成';
    
    overlay.appendChild(message);
    document.body.appendChild(overlay);
    
    // 5秒后自动消失
    setTimeout(() => {
        document.body.removeChild(overlay);
    }, 5000);
}

// 主要的下载流程
async function startDownloadProcess() {
    try {
        console.log('开始下载流程...');
        
        // 发送状态更新
        chrome.runtime.sendMessage({
            action: 'updateStatus',
            message: '正在导航到商品页面...',
            type: 'info'
        });
        
        // 步骤1: 点击"商品"按钮
        if (!window.location.href.includes('item_rank')) {
            console.log('查找商品按钮...');
            const productButton = await waitForElement('span.name[data-spm-anchor-id*="kpSRZI"]');
            if (!productButton || !productButton.textContent.includes('商品')) {
                throw new Error('未找到商品按钮');
            }
            
            console.log('点击商品按钮...');
            await clickElement(productButton);
            
            // 等待页面跳转和加载
            await waitForPageLoad();
        }
        
        chrome.runtime.sendMessage({
            action: 'updateStatus',
            message: '正在下载日数据...',
            type: 'info'
        });
        
        // 步骤2: 点击"日"按钮
        console.log('查找日按钮...');
        const dayButton = await waitForElement('button.ant-btn span');
        const dayButtonParent = Array.from(document.querySelectorAll('button.ant-btn')).find(btn => 
            btn.querySelector('span') && btn.querySelector('span').textContent.trim() === '日'
        );
        
        if (!dayButtonParent) {
            throw new Error('未找到日按钮');
        }
        
        console.log('点击日按钮...');
        await clickElement(dayButtonParent);
        await waitForPageLoad();
        
        // 步骤3: 点击下载按钮（日数据）
        console.log('查找日数据下载按钮...');
        const dayDownloadButton = await waitForElement('.low-common-index-picker-control.outer');
        
        console.log('点击日数据下载按钮...');
        await clickElement(dayDownloadButton);
        
        // 等待下载开始
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        chrome.runtime.sendMessage({
            action: 'updateStatus',
            message: '正在下载7天数据...',
            type: 'info'
        });
        
        // 步骤4: 点击"7天"按钮
        console.log('查找7天按钮...');
        const weekButton = Array.from(document.querySelectorAll('button.ant-btn')).find(btn => 
            btn.querySelector('span') && btn.querySelector('span').textContent.trim() === '7天'
        );
        
        if (!weekButton) {
            throw new Error('未找到7天按钮');
        }
        
        console.log('点击7天按钮...');
        await clickElement(weekButton);
        await waitForPageLoad();
        
        // 步骤5: 点击下载按钮（7天数据）
        console.log('查找7天数据下载按钮...');
        const weekDownloadButton = await waitForElement('.low-common-index-picker-control.outer');
        
        console.log('点击7天数据下载按钮...');
        await clickElement(weekDownloadButton);
        
        // 等待下载完成
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        console.log('下载流程完成！');
        
        // 显示完成消息
        showCompletionMessage();
        
        // 通知popup下载完成
        chrome.runtime.sendMessage({
            action: 'downloadComplete'
        });
        
    } catch (error) {
        console.error('下载过程中出现错误:', error);
        chrome.runtime.sendMessage({
            action: 'downloadError',
            message: error.message
        });
    }
}

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    if (message.action === 'startDownload') {
        startDownloadProcess();
        sendResponse({success: true});
    }
});

console.log('参谋下载数据扩展已加载');
