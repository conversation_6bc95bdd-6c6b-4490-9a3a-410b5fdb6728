document.addEventListener('DOMContentLoaded', function() {
    const downloadBtn = document.getElementById('downloadBtn');
    const status = document.getElementById('status');
    
    function showStatus(message, type = 'info') {
        status.textContent = message;
        status.className = `status ${type}`;
        status.style.display = 'block';
        
        // 3秒后隐藏状态信息
        setTimeout(() => {
            status.style.display = 'none';
        }, 3000);
    }
    
    downloadBtn.addEventListener('click', async function() {
        try {
            // 检查当前标签页是否是目标网站
            const [tab] = await chrome.tabs.query({active: true, currentWindow: true});
            
            if (!tab.url.includes('sycm.taobao.com')) {
                showStatus('请先打开生意参谋网站！', 'error');
                return;
            }
            
            downloadBtn.disabled = true;
            downloadBtn.textContent = '正在执行...';
            showStatus('开始自动下载流程...', 'info');
            
            // 向content script发送消息开始下载流程
            await chrome.tabs.sendMessage(tab.id, {action: 'startDownload'});
            
        } catch (error) {
            console.error('Error:', error);
            showStatus('操作失败，请重试', 'error');
            downloadBtn.disabled = false;
            downloadBtn.textContent = '📊 下载日和7天数据';
        }
    });
    
    // 监听来自content script的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        if (message.action === 'updateStatus') {
            showStatus(message.message, message.type || 'info');
        } else if (message.action === 'downloadComplete') {
            downloadBtn.disabled = false;
            downloadBtn.textContent = '📊 下载日和7天数据';
            showStatus('操作完成！', 'success');
        } else if (message.action === 'downloadError') {
            downloadBtn.disabled = false;
            downloadBtn.textContent = '📊 下载日和7天数据';
            showStatus(message.message || '操作失败', 'error');
        }
    });
});
